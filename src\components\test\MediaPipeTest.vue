<template>
  <div class="mediapipe-test">
    <h2>MediaPipe Face Mesh 测试</h2>
    
    <!-- 状态显示 -->
    <div class="status-panel">
      <p>检测器状态: 
        <span :class="detectorStatus.class">{{ detectorStatus.text }}</span>
      </p>
      <p v-if="lastDetectionResult">
        最后检测结果: 
        <span class="success">
          检测到 {{ lastDetectionResult.points?.length || 0 }} 个关键点，
          {{ lastDetectionResult.smileLipPoints?.length || 0 }} 个唇线点
        </span>
      </p>
      <p v-if="lastDetectionResult?.confidence">
        置信度: {{ (lastDetectionResult.confidence * 100).toFixed(1) }}%
      </p>
    </div>

    <!-- 图片上传区域 -->
    <div class="upload-section">
      <el-upload
        class="upload-demo"
        :show-file-list="false"
        :before-upload="handleImageUpload"
        accept="image/*"
      >
        <el-button type="primary" :loading="isDetecting">
          {{ isDetecting ? '检测中...' : '选择图片进行检测' }}
        </el-button>
      </el-upload>
    </div>

    <!-- 图片显示区域 -->
    <div v-if="imageUrl" class="image-section">
      <h3>原始图片</h3>
      <img 
        ref="imageRef"
        :src="imageUrl" 
        alt="测试图片"
        style="max-width: 500px; max-height: 400px;"
        @load="onImageLoad"
      />
    </div>

    <!-- 检测结果显示 -->
    <div v-if="lastDetectionResult" class="result-section">
      <h3>检测结果</h3>
      <div class="result-grid">
        <div class="result-item">
          <h4>面部关键点 ({{ lastDetectionResult.points?.length || 0 }}个)</h4>
          <div class="points-list">
            <div 
              v-for="(point, index) in lastDetectionResult.points" 
              :key="index"
              class="point-item"
            >
              {{ point.name }}: ({{ point.x.toFixed(1) }}, {{ point.y.toFixed(1) }})
            </div>
          </div>
        </div>
        
        <div class="result-item">
          <h4>唇线点 ({{ lastDetectionResult.smileLipPoints?.length || 0 }}个)</h4>
          <div class="points-summary">
            前5个点: 
            <span v-for="(point, index) in lastDetectionResult.smileLipPoints?.slice(0, 5)" :key="index">
              ({{ point.x.toFixed(1) }}, {{ point.y.toFixed(1) }}){{ index < 4 ? ', ' : '...' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息显示 -->
    <div v-if="errorMessage" class="error-section">
      <el-alert
        :title="errorMessage"
        type="error"
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { ElMessage, ElUpload, ElButton, ElAlert } from 'element-plus'
import { useMediaPipeFaceDetection } from '@/composables/useMediaPipeFaceDetection'
import type { DetectionResult } from '@/types/face'

// 组合式函数
const mediaPipeDetection = useMediaPipeFaceDetection()

// 响应式数据
const imageUrl = ref<string>('')
const imageRef = ref<HTMLImageElement>()
const isDetecting = ref(false)
const lastDetectionResult = ref<DetectionResult | null>(null)
const errorMessage = ref<string>('')

// 计算属性
const detectorStatus = computed(() => {
  if (mediaPipeDetection.isDetectorReady()) {
    return { text: '已就绪', class: 'success' }
  } else {
    return { text: '未初始化', class: 'warning' }
  }
})

/**
 * 处理图片上传
 */
async function handleImageUpload(file: File) {
  try {
    errorMessage.value = ''
    
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      ElMessage.error('请选择图片文件')
      return false
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      ElMessage.error('图片大小不能超过10MB')
      return false
    }

    // 创建图片URL
    imageUrl.value = URL.createObjectURL(file)
    lastDetectionResult.value = null

    return false // 阻止自动上传
  } catch (error) {
    console.error('图片上传处理失败:', error)
    ElMessage.error('图片处理失败')
    return false
  }
}

/**
 * 图片加载完成后自动进行检测
 */
async function onImageLoad() {
  if (!imageRef.value) return

  try {
    isDetecting.value = true
    errorMessage.value = ''

    console.log('开始MediaPipe检测...')
    const result = await mediaPipeDetection.detectFaceFromImage(imageRef.value)
    
    if (result) {
      lastDetectionResult.value = result
      ElMessage.success('面部检测成功！')
      console.log('检测结果:', result)
    } else {
      errorMessage.value = '未检测到面部，请尝试使用清晰的正面人脸照片'
      ElMessage.warning('未检测到面部')
    }
  } catch (error) {
    console.error('MediaPipe检测失败:', error)
    errorMessage.value = `检测失败: ${error instanceof Error ? error.message : '未知错误'}`
    ElMessage.error('面部检测失败')
  } finally {
    isDetecting.value = false
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  if (imageUrl.value) {
    URL.revokeObjectURL(imageUrl.value)
  }
  mediaPipeDetection.dispose()
})
</script>

<style scoped>
.mediapipe-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.status-panel {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status-panel p {
  margin: 5px 0;
}

.success {
  color: #67c23a;
  font-weight: bold;
}

.warning {
  color: #e6a23c;
  font-weight: bold;
}

.upload-section {
  text-align: center;
  margin: 20px 0;
}

.image-section {
  text-align: center;
  margin: 20px 0;
}

.image-section img {
  border: 2px solid #ddd;
  border-radius: 8px;
}

.result-section {
  margin-top: 20px;
}

.result-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 15px;
}

.result-item {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
}

.result-item h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.points-list {
  max-height: 200px;
  overflow-y: auto;
}

.point-item {
  font-family: monospace;
  font-size: 12px;
  margin: 2px 0;
  padding: 2px 5px;
  background: white;
  border-radius: 3px;
}

.points-summary {
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
}

.error-section {
  margin-top: 20px;
}
</style>
