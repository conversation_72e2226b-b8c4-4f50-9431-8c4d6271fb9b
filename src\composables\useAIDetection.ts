import { useFaceData } from './useFaceData'

/**
 * AI检测数据相关功能的组合式函数
 * 专注于从AI获取数据，然后通过useFaceData设置数据
 */
export function useAIDetection() {
  // 使用面部数据管理
  const faceData = useFaceData()

  /**
   * 通过接口获取AI检测数据（面部关键点、唇线点、对齐点等）
   * @param width 画布宽度（可选）
   * @param height 画布高度（可选）
   */
  async function fetchAIDetectionData(width?: number, height?: number) {
    try {
      // 构建API请求URL
      let url = import.meta.env.VITE_API_BASE_URL + '/face/landmark'
      if (width && height) {
        url += `?width=${width}&height=${height}`
      }

      // 发送请求
      const res = await fetch(url)
      if (!res.ok) throw new Error('接口请求失败')

      // 解析响应
      const data = await res.json()

      // 处理响应数据
      if (data && data.data && Array.isArray(data.data.points)) {
        // 使用faceData设置数据
        faceData.setAllFaceData(
          data.data.points,
          data.data.smileLipPoints,
          data.data.mouthLipPoints,
          data.data.smileAlignPoints,
          data.data.mouthAlignPoints
        )
      } else {
        // 响应格式不正确，重置数据
        faceData.resetFaceData()
        console.warn('AI检测接口返回数据格式不正确', data)
      }
    } catch (err) {
      // 请求出错，重置数据
      faceData.resetFaceData()
      console.error('AI检测接口请求或处理出错', err)
    }
  }

  // 返回faceData的所有属性和方法，以及AI检测特有的方法
  return {
    ...faceData,
    fetchAIDetectionData
  }
}
