import * as faceLandmarksDetection from '@tensorflow-models/face-landmarks-detection'
import '@tensorflow/tfjs-backend-webgl'
import { useFaceData } from './useFaceData'
import type { FacePoint, FacePointName } from '@/types/face'

/**
 * MediaPipe Face Mesh检测器组合式函数
 * 提供真实的AI面部关键点检测功能
 */
export function useMediaPipeFaceDetection() {
  const faceData = useFaceData()
  let detector: faceLandmarksDetection.FaceLandmarksDetector | null = null
  let isInitializing = false

  // MediaPipe关键点索引映射到项目所需的6个关键点
  const MEDIAPIPE_KEYPOINT_MAPPING: Record<FacePointName, number> = {
    leftEye: 33,      // 左眼中心
    rightEye: 263,    // 右眼中心
    leftNostril: 219, // 左鼻孔
    rightNostril: 439, // 右鼻孔
    leftMouth: 61,    // 左嘴角
    rightMouth: 291   // 右嘴角
  }

  // 唇部轮廓关键点索引（MediaPipe Face Mesh的唇部区域）
  const LIP_KEYPOINTS = [
    // 外唇轮廓
    61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
    // 内唇轮廓
    78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415,
    // 上唇中心线
    13, 82, 81, 80, 78, 95, 88, 178, 87, 14, 317, 402,
    // 下唇中心线
    15, 16, 17, 18, 200, 199, 175, 0, 269, 270, 267, 271
  ]

  /**
   * 初始化MediaPipe检测器
   */
  async function initializeDetector(): Promise<boolean> {
    if (detector) return true
    if (isInitializing) {
      // 等待初始化完成
      while (isInitializing) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return !!detector
    }

    try {
      isInitializing = true
      console.log('正在初始化MediaPipe Face Mesh检测器...')

      const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh
      const detectorConfig = {
        runtime: 'mediapipe' as const,
        solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh',
        refineLandmarks: true, // 启用更精确的关键点检测
        maxFaces: 1 // 只检测一张脸
      }

      detector = await faceLandmarksDetection.createDetector(model, detectorConfig)
      console.log('MediaPipe Face Mesh检测器初始化成功')
      return true
    } catch (error) {
      console.error('MediaPipe检测器初始化失败:', error)
      return false
    } finally {
      isInitializing = false
    }
  }

  /**
   * 从图片检测面部关键点
   * @param imageElement HTML图片元素
   * @returns 检测结果
   */
  async function detectFaceFromImage(imageElement: HTMLImageElement) {
    try {
      // 确保检测器已初始化
      const initialized = await initializeDetector()
      if (!initialized || !detector) {
        throw new Error('检测器初始化失败')
      }

      console.log('开始检测面部关键点...')
      const startTime = performance.now()

      // 执行面部检测
      const faces = await detector.estimateFaces(imageElement, {
        flipHorizontal: false
      })

      const endTime = performance.now()
      console.log(`面部检测完成，耗时: ${(endTime - startTime).toFixed(2)}ms`)

      if (faces.length === 0) {
        console.warn('未检测到面部')
        return null
      }

      if (faces.length > 1) {
        console.warn(`检测到${faces.length}张面部，使用第一张`)
      }

      // 处理检测结果
      const result = processFaceDetectionResult(faces[0])
      console.log('面部关键点处理完成:', result)
      
      return result
    } catch (error) {
      console.error('面部检测失败:', error)
      throw error
    }
  }

  /**
   * 处理MediaPipe检测结果，转换为项目所需的数据格式
   * @param face MediaPipe检测到的面部数据
   * @returns 转换后的面部数据
   */
  function processFaceDetectionResult(face: any) {
    const keypoints = face.keypoints

    // 提取项目所需的6个关键点
    const facePoints = extractRequiredFacePoints(keypoints)
    
    // 提取唇线点
    const lipPoints = extractLipPoints(keypoints)
    
    // 计算置信度（如果可用）
    const confidence = face.score || 0.9

    return {
      points: facePoints,
      smileLipPoints: lipPoints,
      mouthLipPoints: lipPoints, // 对于单张图片，使用相同的唇线点
      smileAlignPoints: [], // 可以后续添加对齐点提取逻辑
      mouthAlignPoints: [],
      confidence,
      totalKeypoints: keypoints.length
    }
  }

  /**
   * 从MediaPipe的478个关键点中提取项目所需的6个关键点
   * @param keypoints MediaPipe检测到的所有关键点
   * @returns 项目所需的6个关键点
   */
  function extractRequiredFacePoints(keypoints: any[]): FacePoint[] {
    const facePoints: FacePoint[] = []
    
    Object.entries(MEDIAPIPE_KEYPOINT_MAPPING).forEach(([name, index]) => {
      if (index < keypoints.length) {
        const point = keypoints[index]
        facePoints.push({
          name: name as FacePointName,
          x: point.x,
          y: point.y
        })
      }
    })
    
    return facePoints
  }

  /**
   * 从MediaPipe关键点中提取唇线点
   * @param keypoints MediaPipe检测到的所有关键点
   * @returns 唇线点数组
   */
  function extractLipPoints(keypoints: any[]): { x: number; y: number }[] {
    const lipPoints: { x: number; y: number }[] = []
    
    LIP_KEYPOINTS.forEach(index => {
      if (index < keypoints.length) {
        const point = keypoints[index]
        lipPoints.push({
          x: point.x,
          y: point.y
        })
      }
    })
    
    return lipPoints
  }

  /**
   * 销毁检测器，释放资源
   */
  function dispose() {
    if (detector) {
      detector.dispose()
      detector = null
      console.log('MediaPipe检测器已销毁')
    }
  }

  /**
   * 检查检测器是否已初始化
   */
  function isDetectorReady(): boolean {
    return !!detector && !isInitializing
  }

  return {
    // 检测方法
    initializeDetector,
    detectFaceFromImage,
    
    // 工具方法
    dispose,
    isDetectorReady,
    
    // 数据处理方法
    extractRequiredFacePoints,
    extractLipPoints,
    
    // 继承useFaceData的所有方法和属性
    ...faceData
  }
}
